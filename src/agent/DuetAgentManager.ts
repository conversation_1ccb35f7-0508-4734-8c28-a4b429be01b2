import { BaseAgentManager } from './AgentManager';
import { ToolSwitchState } from './utils/toolSwitches';
import { IMessenger } from '@/protocol/messenger';
import { ToCoreProtocol, FromCoreProtocol } from '@/protocol';
import { Chat, ImageBlockParam, TextBlockParamVersion1, ToolUse, WebviewMessage } from '@/agent/types/type';
import { getAvailableMcpServers } from './tools/mcp-tool';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { formatAssistantMessage, formatLlmMessages, parseAssistantMessage } from './tools/parse-assistant-message';
import { EventSourceMessage } from '@fortaine/fetch-event-source';
import { isAllMessagesVersion1 } from './utils/message';
import pWaitFor from 'p-wait-for';
import { v4 } from 'uuid';
import { getDuetAgentToolFunctionSchemas } from './prompt/tools/schema';
import { DUET_SYSTEM_PROMPT } from './prompt/duet';
import { summary } from './prompt/duet/summary';
import { getRulesListForAgent } from './rules';
import { listFiles } from './tools/list-files';
import { ErrorUtils } from '@/util/error';
import { NEED_STOP_TOOL_NAMES } from '@/agent/constants/tool';

type UserContent = (TextBlockParamVersion1 | ImageBlockParam)[];
type AskResponse = 'yesButtonClicked' | 'noButtonClicked' | 'messageResponse';

export class DuetAgentManager extends BaseAgentManager {
  constructor(
    messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
    cwd: string,
    options: {
      model: string;
      maxTokens: number;
      isEnableRepoIndex: boolean;
      isUseNewTool: boolean;
      toolSwitchState?: ToolSwitchState;
      isSupportToolUse: boolean;
      isAskMode?: boolean;
    },
    sessionInfo?: WebviewMessage<'newTask'>
  ) {
    super(messenger, cwd, options, sessionInfo);
  }

  // 异步 constructor，保证前期配置一定加载完成
  static async init(
    messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
    cwd: string,
    sessionInfo?: WebviewMessage<'newTask'>,
    toolSwitchState?: ToolSwitchState,
    isAskMode?: boolean
  ) {
    // 获取基础配置
    const config = await this.getCommonConfig(sessionInfo);

    return new DuetAgentManager(
      messenger,
      cwd,
      {
        ...config,
        toolSwitchState,
        isAskMode
      },
      sessionInfo
    );
  }

  async recursivelyMakeLLMRequests(
    userContent: UserContent,
    includeFileDetails: boolean = false,
    isNewTask: boolean = false
  ): Promise<boolean> {
    this.stateManager.updateState({ startLlmTime: Date.now() });
    if (this.stateManager.getState().abort) throw new Error('Kwaipilot instance aborted');
    // 检查任务是否已完成，如果任务已完成，直接返回true结束循环
    if (this.stateManager.getState().didCompleteTask) return true;

    await this.handleApiRequestLimits(userContent);
    await this.messageService.say('api_req_started', JSON.stringify(userContent));
    this.loggerManager.agentInfo(`废弃旧的abortController，创建新的abortController`);
    this.abortController?.abort();
    this.abortController = new AbortController();
    const innerAbortController = this.abortController;
    // 添加环境信息
    const environmentDetails = await this.messageService.getEnvironmentDetails(includeFileDetails);
    // TODO: @LYT 删除不必要代码
    userContent.unshift({
      type: 'text',
      text: `<project-summary>${summary}</project-summary>`,
      category: 'project-summary'
    });
    userContent.unshift({
      type: 'text',
      text: environmentDetails + '\n\n The current phase is "pre-research"',
      category: 'environment-details'
    });
    await this.messageService.addToApiConversationHistory({
      role: 'user',
      content: userContent,
      chatId: this.stateManager.getState().chatId
    });

    this.stateManager.resetStreamingState();
    this.loggerManager.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-pre-llm',
      millis: Date.now() - this.stateManager.getState().startLlmTime
    });
    try {
      const state = this.stateManager.getState();
      const isSupportToolUse = state.modelConfig.isSupportToolUse;
      const servers = getAvailableMcpServers(this.loggerManager.getAgentLogger());
      const enabledTools = this.stateManager.getEnabledTools();

      const messages =
        isAllMessagesVersion1(state.apiConversationHistory) && this.contextManager
          ? (
              await this.contextManager.optimizeMessagesContext(
                state.apiConversationHistory,
                this.loggerManager.getTrace()
              )
            ).messages
          : state.apiConversationHistory;

      const formattedMessages = isSupportToolUse
        ? formatLlmMessages(messages)
        : messages.map((m) => ({
            content: m.content,
            role: m.role,
            chatId: m.chatId
          }));

      // ============================
      // TODO: @lyt 上线前删除，把 System Prompt 迁移到 kconf 中
      if (isSupportToolUse) {
        formattedMessages.unshift({
          chatId: '111111',
          role: 'system',
          content: DUET_SYSTEM_PROMPT({
            cwd: this.cwd,
            mcpServers: servers,
            shell: '',
            platform: this.sessionInfo?.reqData.deviceInfo?.ide
          }) as unknown as { content: string; type: 'text' }[]
        });
      }
      // ===============================

      const { userRules, projectRules } = getRulesListForAgent(this.sessionInfo?.rules || []);
      const reqParams: Chat.AgentChatRequest = {
        ...(this.sessionInfo?.reqData || {}),
        sessionId: state.sessionId,
        chatId: state.chatId,
        model: state.modelConfig.model,
        tools: isSupportToolUse
          ? getDuetAgentToolFunctionSchemas(
              this.cwd,
              servers,
              this.options.isEnableRepoIndex,
              this.options.isUseNewTool,
              enabledTools
            )
          : undefined,
        messages: formattedMessages,
        mode: isSupportToolUse ? (this.options.isAskMode ? 'ask' : 'agent') : undefined,
        rules: isSupportToolUse
          ? [
              {
                type: Chat.RuleType.USER_RULES,
                content: userRules.filter((rule) => rule).join('\n')
              },
              {
                type: Chat.RuleType.PROJECT_RULES,
                content: projectRules.filter((rule) => rule).join('\n')
              }
            ]
          : undefined,
        systemPrompt: isSupportToolUse ? undefined : state.systemPrompt,
        round: 0,
        environment: isSupportToolUse
          ? {
              ...(await this.messageService.getEnvironmentDetailsV2()),
              workingDirectory: this.cwd,
              workingDirectoryFiles: (await listFiles(this.cwd, false, 100))[0] || []
            }
          : undefined
      };
      let assistantMessage = '';
      this.stateManager.updateState({ isStreaming: true, stopReason: '' });
      this.loggerManager.agentInfo(`历史对话记录messages: ${JSON.stringify(messages)}`);
      const reqUUID = v4();
      // 创建一个 Promise 来追踪所有消息处理
      const messageProcessingPromise = new Promise<void>((resolve, reject) => {
        let isComplete = false;
        let hasError = false;
        let output = '';
        let returnFirstToken = false;
        this.loggerManager.logApiStart(reqUUID, state.modelConfig.model, reqParams);
        const startApiTime = Date.now();

        const baseHeaders = {
          'Content-Type': 'application/json;charset=UTF-8',
          'kwaipilot-username': this.sessionInfo?.reqData.username || '',
          'kwaipilot-platform': this.sessionInfo?.reqData.deviceInfo?.platform || ''
        };
        const headers: Record<string, string> = this.sessionInfo?.reqData.jwtToken
          ? {
              Authorization: `Bearer ${this.sessionInfo?.reqData.jwtToken}`,
              ...baseHeaders
            }
          : {
              ...baseHeaders
            };

        const chatUrl = isSupportToolUse
          ? '/eapi/kwaipilot/plugin/composer/v2/chat/completions'
          : '/eapi/kwaipilot/plugin/composer/chat/completions';

        this.httpClient
          .fetchEventSource(chatUrl, {
            method: 'POST',
            body: JSON.stringify(reqParams),
            headers,
            signal: innerAbortController?.signal,
            onclose: () => {
              this.loggerManager.logApiEnd(reqUUID, state.modelConfig.model, Date.now() - startApiTime);
              if (innerAbortController?.signal.aborted) {
                this.loggerManager.agentInfoWithRequestId(reqUUID, '模型请求接口关闭，fetchEventSource aborted');
                this.loggerManager.endGenerationWithMessage('模型请求接口关闭，fetchEventSource aborted');
                return;
              }
              if (!isComplete) {
                resolve();
              }
            },
            onmessage: async (event: EventSourceMessage) => {
              if (!returnFirstToken) {
                this.loggerManager.logFirstToken(reqUUID, state.modelConfig.model, Date.now() - startApiTime);
                returnFirstToken = true;
              }
              if (innerAbortController?.signal.aborted) {
                this.loggerManager.agentInfoWithRequestId(reqUUID, 'fetchEventSource aborted');
                return;
              }
              const data = JSON.parse(event.data);
              const traceId = data.traceId;
              if (data.code === 413) {
                this.stateManager.updateState({
                  isTooLongForApiReq: true,
                  tooLongTip: data.tip
                });
                this.loggerManager.logApiTooLongError(
                  reqUUID,
                  state.modelConfig.model,
                  Date.now() - state.startLlmTime
                );
                return;
              }
              if (data.code === 4131) {
                this.stateManager.updateState({ stopReason: 'result_token_isTooLong' });
                this.loggerManager.agentInfo('回复token超长：', { code: data.code });
                return;
              }
              const isSupportToolUse = this.stateManager.getState().modelConfig.isSupportToolUse;

              const usage = isSupportToolUse ? data?.data?.usage : data?.usage;

              if (usage) {
                this.loggerManager.reportAgentTask('agent_task_token', {
                  requestId: reqUUID,
                  sessionId: this.stateManager.getState().sessionId,
                  chatId: this.stateManager.getState().chatId,
                  ts: Date.now(),
                  duration: Date.now() - this.stateManager.getState().startLlmTime,
                  modelName: this.stateManager.modelConfig.model,
                  usage: usage
                });
              }

              try {
                let delta = '';

                /** xml格式 工具调用 */
                if (!isSupportToolUse) {
                  delta = data?.message?.content;
                } else {
                  /** json schema格式 工具调用 */
                  const content = data?.data?.choices?.[0]?.message?.content;
                  if (content && Array.isArray(content)) {
                    for (let item of content) {
                      if (item.type === 'text') {
                        delta += item.text;
                      } else if (item.type === 'image') {
                        delta += item?.source?.url;
                      }
                    }
                  } else if (typeof content === 'string') {
                    delta += content;
                  }
                }

                assistantMessage += delta || '';
                output = assistantMessage;
                this.loggerManager.agentDebug(`流式返回信息 chunk: ${JSON.stringify(data)}`);
                this.loggerManager.agentInfoWithTrace(reqUUID, traceId, `流式返回信息元: ${delta}`);
                this.loggerManager.agentDebug(`流式返回信息（增量）: ${assistantMessage}`);
                const currentState = this.stateManager.getState();
                const prevLength = currentState.assistantMessageContent.length;

                const newAssistantMessageContent = isSupportToolUse
                  ? formatAssistantMessage(
                      data?.data?.choices?.[0]?.message,
                      this.stateManager.getState().assistantMessageContent
                    )
                  : parseAssistantMessage(assistantMessage);
                this.stateManager.updateState({ assistantMessageContent: newAssistantMessageContent });

                if (newAssistantMessageContent.length > prevLength) {
                  this.stateManager.updateState({ userMessageContentReady: false });
                }

                this.presentAssistantMessage();
                if (currentState.didRejectTool) {
                  this.loggerManager.agentInfoWithRequestId(reqUUID, 'didRejectTool:innerAbortController?.abort()');
                  innerAbortController?.abort();
                  isComplete = true;
                  assistantMessage += '\n\n[Response interrupted by user feedback]';
                  resolve();
                }
                if (currentState.didAlreadyUseTool) {
                  this.loggerManager.agentInfoWithRequestId(reqUUID, 'didAlreadyUseTool:innerAbortController?.abort()');
                  innerAbortController?.abort();
                  isComplete = true;
                  assistantMessage +=
                    '\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]';
                  resolve();
                }
                return;
              } catch (error: any) {
                this.loggerManager.logMessageParseError(reqUUID, error);
                this.messageService.say('error', `消息解析出错: ${error}`);
                reject(error);
              }
            },
            onerror: (e: any) => {
              this.loggerManager.logApiError(
                reqUUID,
                state.modelConfig.model,
                Date.now() - startApiTime,
                ErrorUtils.errorToJson(e)
              );
              if (!hasError && !isComplete) {
                // 处理错误...
                hasError = true;
                isComplete = true;
                reject(e);
              }
            }
          })
          .then(() => {
            this.loggerManager.updateTrace(output);
            this.loggerManager.endGeneration(output);
          });
      });
      try {
        // 使用Promise包装事件流处理
        await messageProcessingPromise;
        this.loggerManager.agentInfoWithRequestId(reqUUID, '消息处理已完成:' + assistantMessage);
      } catch (error) {
        // 捕获并记录错误，但仍然继续执行
        this.loggerManager.logMessageProcessError(reqUUID, error);
      } finally {
        // 后续处理
        this.stateManager.updateState({
          isStreaming: false,
          didCompleteReadingStream: true
        });

        const currentState = this.stateManager.getState();
        const partialBlocks = currentState.assistantMessageContent.filter((block) => block.partial);
        partialBlocks.forEach((block) => {
          block.partial = false;
          if (block.type === 'tool_use') {
            const toolUse = block as ToolUse;
            if (toolUse.paramsString) {
              try {
                toolUse.params = JSON.parse(toolUse.paramsString);
                toolUse.paramsString = '';
              } catch (error) {
                this.loggerManager.agentInfo(`Failed to parse tool use params: ${toolUse.paramsString}`);
                toolUse.params = {};
              }
            }
          }
        });
        if (partialBlocks.length > 0) {
          // 强制更新：通过重新设置相同的引用，确保状态管理器知道内容已经改变
          this.stateManager.updateState({ assistantMessageContent: currentState.assistantMessageContent });

          this.presentAssistantMessage();
        }
        let didEndLoop = false;
        if (currentState.assistantMessageContent.length > 0) {
          await this.messageService.addToApiConversationHistory({
            role: 'assistant',
            content: [
              {
                type: 'text',
                text: assistantMessage,
                category: 'assistant'
              }
            ],
            chatId: currentState.chatId
          });
          await pWaitFor(() => this.stateManager.getState().userMessageContentReady);
          const didToolUse = currentState.assistantMessageContent.some((block) => block.type === 'tool_use');

          if (!didToolUse) {
            await this.handleCompletedTask();
            return true;
          }
          this.loggerManager.logLLMSuccess(
            reqUUID,
            currentState.modelConfig.model,
            Date.now() - currentState.startLlmTime
          );

          const recDidEndLoop = await this.recursivelyMakeLLMRequests(currentState.userMessageContent);
          didEndLoop = recDidEndLoop || currentState.didCompleteTask;
        } else {
          if (!currentState.isTooLongForApiReq) {
            // await this.say('api_req_failed', 'API请求出错');
            this.loggerManager.logLLMError(
              reqUUID,
              currentState.modelConfig.model,
              Date.now() - currentState.startLlmTime,
              'api_req_failed'
            );
          }
          await this.messageService.addToApiConversationHistory({
            role: 'assistant',
            content: [
              {
                type: 'text',
                text: 'Failure: I did not provide a response.',
                category: 'assistant'
              }
            ],
            chatId: currentState.chatId
          });
          this.stateManager.updateState({
            consecutiveMistakeCount: currentState.consecutiveMistakeCount + 1
          });
        }

        return didEndLoop;
      }
    } catch (error: any) {
      const currentState = this.stateManager.getState();
      this.loggerManager.logLLMProcessError(
        currentState.modelConfig.model,
        Date.now() - currentState.startLlmTime,
        error
      );
      return true;
    }
  }

  async beforeCompletedTask(){
    // 前面有
      await this.messageService.say('research_end', '');
  }
}
