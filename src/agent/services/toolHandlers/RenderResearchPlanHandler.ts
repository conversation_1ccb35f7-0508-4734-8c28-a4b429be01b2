import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { SayTool, TextBlockParamVersion1 } from '../../types/type';
import { RENDER_RESEARCH_PLAN_TOOL_NAME } from '@/agent/constants/tool';
import { LangfuseGenerationClient } from 'langfuse';

/**
 * Grep搜索工具处理器
 */
export class RenderResearchPlanHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const content: string | undefined = block.params.content;
    const sharedMessageProps: SayTool = {
      tool: 'renderResearchPlan',
      content,
    };
    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: ''
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        if (!content) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateMissingParamError(RENDER_RESEARCH_PLAN_TOOL_NAME, 'content', this.context),
            this.context.stateManager
          );
          return;
        }
        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: RENDER_RESEARCH_PLAN_TOOL_NAME
        });
        await this.context.messageService.say('tool', JSON.stringify(sharedMessageProps), false);

        const toolLog = ToolHelpers.generateToolLog(RENDER_RESEARCH_PLAN_TOOL_NAME, this.context.loggerManager);
        toolLog.start(`${content}`);
        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            path: content
          },
          metadata: {
            name: block.name
          }
        });
        const startToolTime = Date.now();
        toolLog.end('success');
        generationCall?.end({

        });
        // const completeMessage = JSON.stringify({
        //   ...sharedMessageProps,
        //   content: results
        // } satisfies SayTool);
        // await this.context.messageService.say('tool', completeMessage, false);
        ToolHelpers.pushToolResult(block, userMessageContent, "the research plan has been rendered successfully", this.context.stateManager);
        // this.context.loggerManager.perf({
        //   namespace: ASSISTANT_NAMESPACE,
        //   subtag: 'kwaipilot-ide-agent-chat-tool',
        //   millis: Date.now() - startToolTime,
        //   extra4: 'success',
        //   extra6: block.name
        // });
        // ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
        //   path: absolutePath,
        //   regex,
        //   filePattern
        // });
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'render research plan',
        error,
        RENDER_RESEARCH_PLAN_TOOL_NAME
      );
    }
  }
} 