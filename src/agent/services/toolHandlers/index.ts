// 导入所有处理器类
import { Tool<PERSON>andlerContext, ToolHandler } from '../ToolHelpers';
import { EditFileHandler } from './EditFileHandler';
import { ReadFileHandler } from './ReadFileHandler';
import { ExecuteCommandHandler } from './ExecuteCommandHandler';
import { SearchAndReplaceHandler } from './SearchAndReplaceHandler';
import { WriteToFileHandler } from './WriteToFileHandler';
import { CodebaseSearchHandler } from './CodebaseSearchHandler';
import { ListFilesHandler } from './ListFilesHandler';
import { GrepSearchHandler } from './GrepSearchHandler';
import { UseMcpToolHandler } from './UseMcpToolHandler';
import { AskFollowupQuestionHandler } from './AskFollowupQuestionHandler';
import { CommandStatusCheckHandler } from './CommandStatusCheckHandler';
import { WebSearchHandler } from './WebSearchHandler';
import { WebFetchHandler } from './WebFetchHandler';
import { ProjectPreviewHandler } from './ProjectPreviewHandler';
import { ParseFigmaHandler } from './ParseFigmaHandler';
import { RenderResearchPlanHandler } from './RenderResearchPlanHandler';
import { RENDER_RESEARCH_PLAN_TOOL_NAME } from '@/agent/constants/tool';
import { UpdatePhaseHandler } from './UpdatePhaseHandler';

// 导出基础类型和接口
export { ToolHandlerContext, ToolHandler };

// 导出所有工具处理器类
export {
  EditFileHandler,
  ReadFileHandler,
  ExecuteCommandHandler,
  SearchAndReplaceHandler,
  WriteToFileHandler,
  CodebaseSearchHandler,
  ListFilesHandler,
  GrepSearchHandler,
  UseMcpToolHandler,
  AskFollowupQuestionHandler,
  WebSearchHandler,
  WebFetchHandler,
  ParseFigmaHandler
};

// 便于使用的工具处理器映射
export const TOOL_HANDLERS = {
  update_phase: UpdatePhaseHandler,
  [RENDER_RESEARCH_PLAN_TOOL_NAME]: RenderResearchPlanHandler,
  edit_file: EditFileHandler,
  read_file: ReadFileHandler,
  execute_command: ExecuteCommandHandler,
  replace_in_file: SearchAndReplaceHandler,
  write_to_file: WriteToFileHandler,
  codebase_search: CodebaseSearchHandler,
  list_files: ListFilesHandler,
  grep_search: GrepSearchHandler,
  use_mcp_tool: UseMcpToolHandler,
  ask_followup_question: AskFollowupQuestionHandler,
  command_status_check: CommandStatusCheckHandler,
  project_preview: ProjectPreviewHandler,
  parse_figma: ParseFigmaHandler,
  web_search: WebSearchHandler,
  web_fetch: WebFetchHandler
} as const;

// 工具名称类型
export type ToolName = keyof typeof TOOL_HANDLERS;

// 工具处理器实例类型
export type ToolHandlerInstance = InstanceType<(typeof TOOL_HANDLERS)[ToolName]>;
