import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON>el<PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { SayTool, TextBlockParamVersion1 } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { LangfuseGenerationClient } from 'langfuse';
import { Api } from '@/http';

/**
 * Web搜索工具处理器
 */
export class WebSearchHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const query: string | undefined = block.params.query;
    const hl: string | undefined = block.params.hl || 'en';
    const gl: string | undefined = block.params.gl || 'us';

    const sharedMessageProps: SayTool = {
      tool: 'webSearch',
      query: ToolHelpers.removeClosingTag('query', query, block.partial),
      hl: ToolHelpers.removeClosingTag('hl', hl, block.partial),
      gl: ToolHelpers.removeClosingTag('gl', gl, block.partial)
    };

    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: ''
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        if (!query) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateMissingParamError('web_search' as any, 'query', this.context),
            this.context.stateManager
          );
          return;
        }

        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'web_search'
        });

        const toolLog = ToolHelpers.generateToolLog('web_search', this.context.loggerManager);
        toolLog.start(`searching for: ${query} (hl=${hl}, gl=${gl})`);

        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            query,
            hl,
            gl
          },
          metadata: {
            name: block.name
          }
        });

        const startToolTime = Date.now();
        const results = await this.performWebSearch(query, hl || 'en', gl || 'us');
        toolLog.end(results);

        generationCall?.end({
          output: { results }
        });

        const completeMessage = JSON.stringify({
          ...sharedMessageProps,
          content: results
        } satisfies SayTool);

        await this.context.messageService.say('tool', completeMessage, false);
        ToolHelpers.pushToolResult(block, userMessageContent, results, this.context.stateManager);

        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });

        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          query,
          hl: hl || 'en',
          gl: gl || 'us'
        });
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'web search',
        error,
        'web_search' as any
      );
    }
  }

  private async performWebSearch(query: string, hl: string, gl: string): Promise<string> {
    try {
      const api = new Api();

      // 创建 FormData 对象来模拟 application/x-www-form-urlencoded
      const formData = new FormData();
      formData.append('query', query);
      formData.append('hl', hl);
      formData.append('gl', gl);

      // 设置请求头
      const headers = {
        'x-kwaipilot-token': '470be6af-d872-4c10-ae19-6b46f4326c39',
        'x-kwaipilot-from': 'search',
        'Content-Type': 'application/x-www-form-urlencoded'
      };

      // 由于需要使用 application/x-www-form-urlencoded，我们使用 URLSearchParams
      const params = new URLSearchParams();
      params.append('query', query);
      params.append('hl', hl);
      params.append('gl', gl);

      const response = await api.post<any>('/node/api/search/custom-search', params.toString(), {
        headers
      });

      return typeof response === 'string' ? response : JSON.stringify(response);
    } catch (error: any) {
      throw new Error(`Failed to perform web search: ${error.message}`);
    }
  }
}
