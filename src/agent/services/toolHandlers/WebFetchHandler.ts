import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { SayTool, TextBlockParamVersion1 } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { LangfuseGenerationClient } from 'langfuse';
import { Api } from '@/http';

/**
 * Web获取工具处理器
 */
export class WebFetchHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const url: string | undefined = block.params.url;
    const nocache: string | undefined = block.params.nocache || 'true';

    const sharedMessageProps: SayTool = {
      tool: 'webFetch',
      url: ToolHelpers.removeClosingTag('url', url, block.partial),
      nocache: ToolHelpers.removeClosingTag('nocache', nocache, block.partial)
    };

    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: ''
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        if (!url) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateMissingParamError('web_fetch' as any, 'url', this.context),
            this.context.stateManager
          );
          return;
        }

        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'web_fetch'
        });

        const toolLog = ToolHelpers.generateToolLog('web_fetch', this.context.loggerManager);
        toolLog.start(`fetching: ${url} (nocache=${nocache})`);

        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            url,
            nocache
          },
          metadata: {
            name: block.name
          }
        });

        const startToolTime = Date.now();
        const results = await this.performWebFetch(url, nocache || 'true');
        toolLog.end(results);

        generationCall?.end({
          output: { results }
        });

        const completeMessage = JSON.stringify({
          ...sharedMessageProps,
          content: results
        } satisfies SayTool);

        await this.context.messageService.say('tool', completeMessage, false);
        ToolHelpers.pushToolResult(block, userMessageContent, results, this.context.stateManager);

        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });

        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          url,
          nocache: nocache || 'true'
        });
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'web fetch',
        error,
        'web_fetch' as any
      );
    }
  }

  private async performWebFetch(url: string, nocache: string): Promise<string> {
    try {
      const api = new Api();

      // 构建查询参数
      const queryParams = new URLSearchParams();
      queryParams.append('url', url);
      queryParams.append('nocache', nocache);

      // 设置请求头
      const headers = {};

      const response = await api.get<any>(
        `/node/api/reader/fetcher?${queryParams.toString()}`,
        {},
        {
          headers
        }
      );

      return typeof response === 'string' ? response : JSON.stringify(response);
    } catch (error: any) {
      throw new Error(`Failed to fetch web content: ${error.message}`);
    }
  }
}
