export const summary = `
# 产品概述

## 项目简介

这是一个名为 "knockKnock-game" 的 React 项目，基于持续更新的 React 项目起始模板构建。该项目旨在简化启动新项目的流程，提供了一个基础配置和最佳实践的基础，让开发者可以专注于构建应用程序，而无需设置基础设施。

## 核心特性

- **现代化 React 应用**: 基于 React 18 和 TypeScript 构建
- **国际化支持**: 集成 react-i18next 提供多语言支持
- **状态管理**: 使用 Zustand 进行轻量级状态管理
- **数据获取**: 集成 React Query 进行服务端状态管理和缓存
- **UI 组件库**: 使用 Ant Design 作为主要 UI 组件库
- **样式系统**: 采用 Tailwind CSS 实用优先的样式框架
- **代码生成**: 支持从 OpenAPI/Swagger 自动生成 TypeScript 类型和 API 客户端代码

## 目标用户

- 前端开发者
- 需要快速启动 React 项目的团队
- 希望使用现代化技术栈的开发者

## 项目定位

这是一个企业级的 React 项目模板，强调开发效率、代码质量和最佳实践。

# 技术栈和构建系统

## 核心技术栈

### 前端框架

- **React 18**: 主要 UI 框架
- **TypeScript**: 类型安全的 JavaScript 超集
- **React Router 7**: 客户端路由管理

### 状态管理

- **Zustand**: 轻量级状态管理器
- **React Query (@tanstack/react-query)**: 服务端状态管理和数据缓存

### UI 和样式

- **Ant Design**: 主要 UI 组件库
- **Tailwind CSS**: 实用优先的 CSS 框架
- **@team/antd-config-provider**: 自定义 Ant Design 配置

### 构建工具

- **Vite**: 现代化构建工具和开发服务器
- **pnpm**: 包管理器（要求 >=9.x.x）
- **Node.js**: 运行时环境（要求 >=22.x.x）

### 代码质量工具

- **Biome**: 代码格式化和 Linting 工具
- **Husky**: Git hooks 管理
- **lint-staged**: 预提交代码检查
- **CommitLint**: 提交信息规范检查
- **knip**: 未使用代码检测

### 测试框架

- **Vitest**: 测试框架
- **@testing-library/react**: React 组件测试
- **jsdom**: DOM 环境模拟

### 代码生成

- **Kubb**: OpenAPI 到 TypeScript/React Query 代码生成
- **unplugin-kubb**: Vite 插件集成

### 国际化

- **react-i18next**: 国际化解决方案
- **i18next**: 核心国际化库

## 常用命令

### 开发环境

\`\`\`bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev
# 或
pnpm start

# 类型检查
pnpm type-check
\`\`\`

### 代码质量

\`\`\`bash
# 代码格式化
pnpm format

# 代码检查
pnpm lint

# 代码检查和自动修复
pnpm check

# 危险的自动修复（包含不安全的修复）
pnpm check:unsafe

# 检测未使用的代码
pnpm knip
\`\`\`

### 测试

\`\`\`bash
# 运行测试
pnpm test

# 测试 UI 界面
pnpm test:ui

# 测试覆盖率
pnpm test:coverage
\`\`\`

### 构建和部署

\`\`\`bash
# 构建项目
pnpm build

# 预览构建结果
pnpm preview

# 发布版本
pnpm release

# 清理项目
pnpm clean
\`\`\`

### 代码生成

\`\`\`bash
# 从 OpenAPI/Swagger 生成代码
pnpm generate
\`\`\`

### 其他工具

\`\`\`bash
# 依赖更新检查
pnpm taze

# React 性能扫描
pnpm scan
\`\`\`

## 配置文件

### 构建配置

- \`vite.config.mts\`: Vite 构建配置
- \`tsconfig.json\`: TypeScript 配置
- \`postcss.config.mjs\`: PostCSS 配置
- \`tailwind.config.ts\`: Tailwind CSS 配置

### 代码质量配置

- \`biome.json\`: Biome 配置
- \`.commitlintrc.json\`: CommitLint 配置
- \`.lintstagedrc\`: lint-staged 配置
- \`knip.ts\`: knip 配置

### 环境配置

- \`env/\`: 环境变量目录
- \`.env.example\`: 环境变量示例文件

## 开发服务器配置

- 默认端口: 5173
- 自动打开浏览器
- 支持代理配置（通过 VITE_PROXY_TARGET 环境变量）
- 热模块替换 (HMR) 支持



# 项目结构和组织规范

## 整体架构

项目遵循 [Feature-Sliced Design](https://feature-sliced.github.io/documentation/) 架构模式，采用分层和模块化的组织方式。

## 目录结构

\`\`\`
knockKnock-game/
├── .kwaipilot/          # Kwaipilot 配置和规范文档
├── .husky/              # Git hooks 配置
├── .vscode/             # VS Code 配置
├── env/                 # 环境变量配置
├── public/              # 静态资源
├── src/                 # 源代码目录
│   ├── app/             # 应用层 - 应用初始化和全局配置
│   ├── pages/           # 页面层 - 路由页面组件
│   ├── widgets/         # 组件层 - 复杂业务组件
│   ├── components/      # 通用组件
│   ├── shared/          # 共享层 - 通用工具和服务
│   ├── models/          # 数据模型
│   ├── services/        # 业务服务
│   ├── controllers/     # 控制器
│   ├── config/          # 配置文件
│   ├── store/           # 状态管理
│   ├── types/           # 类型定义
│   └── utils/           # 工具函数
└── tests/               # 测试配置和工具
\`\`\`

## 分层说明

### App 层 (\`src/app/\`)

- 应用程序的入口点和全局配置
- 包含根组件、全局提供者、错误边界等
- 文件: \`App.tsx\`, \`index.tsx\`, \`index.css\`

### Pages 层 (\`src/pages/\`)

- 路由页面组件
- 每个页面对应一个路由
- 组织方式: 按功能模块分组

### Widgets 层 (\`src/widgets/\`)

- 复杂的业务组件
- 可能包含自己的状态管理
- 组织方式: 每个 widget 包含 \`ui/\`, \`model/\` 子目录

### Components 层 (\`src/components/\`)

- 可复用的通用组件
- 不包含业务逻辑
- 按组件类型或功能分组

### Shared 层 (\`src/shared/\`)

- 共享的工具、服务和配置
- 子目录结构:
  - \`lib/\`: 第三方库配置和封装
  - \`services/\`: API 服务和数据层
  - \`utils/\`: 通用工具函数
  - \`assets/\`: 静态资源

## 代码生成目录

### API 服务 (\`src/shared/services/\`)

通过 Kubb 从 OpenAPI/Swagger 自动生成:

- \`api/\`: API 客户端代码
- \`model/\`: TypeScript 类型定义
- \`react-query/\`: React Query hooks

## 命名规范

### 文件命名

- 组件文件: PascalCase (如 \`UserProfile.tsx\`)
- 工具文件: camelCase (如 \`formatDate.ts\`)
- 配置文件: kebab-case (如 \`github-oauth.ts\`)
- 测试文件: 与源文件同名 + \`.test.ts\` 或 \`.spec.ts\`

### 目录命名

- 功能目录: kebab-case (如 \`user-profile/\`)
- 组件目录: PascalCase (如 \`UserProfile/\`)

### 导入路径

- 使用 \`@/\` 别名指向 \`src/\` 目录
- 示例: \`import { Button } from '@/components/Button'\`

## 组件组织模式

### Widget 组织结构

\`\`\`
widgets/DemoModal/
├── ui/
│   └── DemoModal.tsx     # UI 组件
├── model/
│   └── useDemoModalStore.ts  # 状态管理
└── index.ts              # 导出文件
\`\`\`

### 服务组织结构

\`\`\`
services/
├── github-api.service.ts
├── github-user.service.ts
├── session.service.ts
├── index.ts              # 统一导出
└── __tests__/            # 测试文件
\`\`\`

## 测试组织

- 测试文件与源文件同目录，使用 \`__tests__/\` 子目录
- 测试文件命名: \`*.test.ts\` 或 \`*.spec.ts\`
- 全局测试配置: \`tests/setup.ts\`

## 配置文件位置

- 环境变量: \`env/\` 目录
- 应用配置: \`src/config/\` 目录
- 构建配置: 项目根目录

## 静态资源

- 公共静态资源: \`public/\` 目录
- 组件相关资源: \`src/shared/assets/\` 目录

## 导入导出规范

- 每个目录应有 \`index.ts\` 文件统一导出
- 优先使用命名导出而非默认导出
- 按字母顺序组织导入语句

`;