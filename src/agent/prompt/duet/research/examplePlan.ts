export const SPEC_WORKFLOW_EXAMPLE_PLAN = `
**Example Format (truncated):**

\`\`\`markdown
# Implementation Plan

- [ ] 1. Set up project structure and core interfaces
   - Create directory structure for models, services, repositories, and API components
   - Define interfaces that establish system boundaries
   - _Requirements: 1.1_

- [ ] 2. Create core data model interfaces and types
  - Write TypeScript interfaces for all data models
  - Implement validation functions for data integrity
  - _Requirements: 2.1, 3.3, 1.2_

- [ ] 3. Implement User model with validation
  - Write User class with validation methods
  - Create unit tests for User model validation
  - _Requirements: 1.2_

- [ ] 4. Implement Document model with relationships
  - Code Document class with relationship handling
  - Write unit tests for relationship management
  - _Requirements: 2.1, 3.3, 1.2_

- [ ] 5. Implement database connection utilities
  - Write connection management code
  - Create error handling utilities for database operations
  - _Requirements: 2.1, 3.3, 1.2_

- [ ] 6. Implement repository pattern for data access
  - Code base repository interface
  - Implement concrete repositories with CRUD operations
  - Write unit tests for repository operations
  - _Requirements: 4.3_

[Additional coding tasks continue...]
\`\`\`
`;

